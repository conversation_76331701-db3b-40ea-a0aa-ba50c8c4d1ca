"use client";

import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

export default function Home() {
  // State to control animation visibility
  const [isClient, setIsClient] = useState(false);

  // Use useEffect to set isClient to true after component mounts
  useEffect(() => {
    setIsClient(true);
  }, []);
  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section */}
      <section className="hero-gradient w-full py-20 md:py-32">
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            {/* Logo grande en la parte superior */}
            <div className="flex justify-center mb-8">
              <div className="bg-white rounded-full p-4 shadow-lg">
                <Image
                  src="/images/logo1.svg"
                  alt="DOMUS OTEC Logo"
                  width={240}
                  height={240}
                  className="w-48 h-auto"
                  style={{ filter: "drop-shadow(0px 4px 8px rgba(0, 0, 0, 0.15))" }}
                  priority
                />
              </div>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-6 leading-tight">
              DOMUS OTEC
            </h1>
            <p className="text-xl md:text-2xl text-white/90 mb-10 leading-relaxed">
              Plataforma de certificación educativa de calidad para profesionales y empresas.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/login"
                className="bg-white text-primary px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-300 shadow-lg whitespace-nowrap inline-flex items-center justify-center"
              >
                Iniciar Sesión
              </Link>
              <Link
                href="/register"
                className="btn-gradient text-white px-8 py-3 rounded-lg font-medium shadow-lg whitespace-nowrap transition-all duration-300 inline-flex items-center justify-center"
              >
                Registrarse
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className={`py-20 section-fade subtle-pattern ${isClient ? '' : 'visible'}`} id="features">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <span className="text-primary font-medium uppercase tracking-wider">FUNCIONALIDADES</span>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mt-3 mb-6">Todo lo que necesitas en una plataforma</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Gestiona alumnos, certificados y evaluaciones de forma sencilla y eficiente.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {/* Feature 1 */}
            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-102">
              <div className="w-14 h-14 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
                <svg className="h-6 w-6 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-3">Gestión de Certificados</h3>
              <p className="text-gray-600">
                Emite, administra y verifica certificados con códigos QR para validación instantánea.
              </p>
            </div>
            {/* Feature 2 */}
            <Link href="/cursos" className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-102">
              <div className="w-14 h-14 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
                <svg className="h-6 w-6 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-3">Cursos de Capacitación</h3>
              <p className="text-gray-600">
                Explora nuestra oferta de cursos especializados con certificación oficial.
              </p>
              <div className="mt-4 text-primary font-medium flex items-center">
                <span>Ver cursos</span>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-1" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </div>
            </Link>
            {/* Feature 3 */}
            <div className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-102">
              <div className="w-14 h-14 bg-primary/10 rounded-lg flex items-center justify-center mb-6">
                <svg className="h-6 w-6 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-gray-800 mb-3">Evaluaciones y Notas</h3>
              <p className="text-gray-600">
                Registra asistencia, evaluaciones y calificaciones de forma organizada y accesible.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Verification Section */}
      <section className={`py-20 bg-gray-50 section-fade ${isClient ? '' : 'visible'}`} id="verification">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <span className="text-primary font-medium uppercase tracking-wider">VERIFICACIÓN QR</span>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mt-3 mb-6">Validación instantánea de certificados</h2>
            <p className="text-lg text-gray-600 mb-10">
              Cada certificado incluye un código QR único que permite verificar su autenticidad en segundos.
            </p>
            <div className="flex justify-center mb-10">
              <div className="w-32 h-32 flex items-center justify-center">
                <svg className="h-24 w-24 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                </svg>
              </div>
            </div>
            <Link
              href="/verificar-certificado"
              className="btn-gradient text-white px-8 py-3 rounded-lg font-medium shadow-lg whitespace-nowrap transition-all duration-300 inline-flex items-center justify-center"
            >
              Verificar un Certificado
            </Link>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className={`py-20 section-fade subtle-pattern ${isClient ? '' : 'visible'}`} id="benefits">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid md:grid-cols-2 gap-16 items-center">
              <div>
                <span className="text-primary font-medium uppercase tracking-wider">BENEFICIOS</span>
                <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mt-3 mb-6">Potencia tu institución educativa</h2>
                <p className="text-lg text-gray-600 mb-8">
                  Nuestra plataforma está diseñada para optimizar todos los procesos educativos, desde la gestión de estudiantes hasta la emisión de certificados oficiales.
                </p>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="w-6 h-6 mr-4 text-primary flex-shrink-0 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-gray-700">Reduce el tiempo administrativo en hasta un 70%</p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-6 h-6 mr-4 text-primary flex-shrink-0 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-gray-700">Certificados con validación oficial y verificables en línea</p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-6 h-6 mr-4 text-primary flex-shrink-0 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-gray-700">Seguimiento detallado del progreso de cada estudiante</p>
                  </div>
                  <div className="flex items-start">
                    <div className="w-6 h-6 mr-4 text-primary flex-shrink-0 mt-1">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <p className="text-gray-700">Acceso desde cualquier dispositivo, en cualquier momento</p>
                  </div>
                </div>
              </div>
              <div className="relative">
                <div className="bg-white p-1 rounded-xl shadow-xl relative z-10">
                  <Image
                    src="/images/logo1.svg"
                    alt="DOMUS OTEC"
                    width={600}
                    height={400}
                    className="rounded-lg w-full h-auto object-contain p-8"
                    style={{ filter: "drop-shadow(0px 4px 12px rgba(0, 0, 0, 0.15))" }}
                  />
                </div>
                <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-primary/10 rounded-full z-0"></div>
                <div className="absolute -top-6 -left-6 w-24 h-24 bg-secondary/10 rounded-full z-0"></div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className={`py-20 section-fade ${isClient ? '' : 'visible'}`} id="contact">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <span className="text-primary font-medium uppercase tracking-wider">CONTACTO</span>
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mt-3 mb-6">¿Necesitas ayuda?</h2>
            <p className="text-lg text-gray-600 mb-10">
              Contáctanos para obtener más información sobre nuestros servicios educativos.
            </p>
            <div className="bg-white p-8 rounded-xl shadow-lg max-w-xl mx-auto">
              <form>
                <div className="mb-6">
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2 text-left">Nombre completo</label>
                  <input type="text" id="name" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary transition-colors duration-300" placeholder="Tu nombre" />
                </div>
                <div className="mb-6">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2 text-left">Correo electrónico</label>
                  <input type="email" id="email" className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary transition-colors duration-300" placeholder="<EMAIL>" />
                </div>
                <div className="mb-6">
                  <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2 text-left">Mensaje</label>
                  <textarea id="message" rows={4} className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:border-primary transition-colors duration-300" placeholder="¿En qué podemos ayudarte?" />
                </div>
                <button type="submit" className="btn-gradient text-white px-8 py-3 rounded-lg font-medium shadow-lg w-full whitespace-nowrap transition-all duration-300">
                  Enviar mensaje
                </button>
              </form>
            </div>
            <div className="mt-10">
              <p className="text-gray-600">O escríbenos directamente a:</p>
              <a href="mailto:<EMAIL>" className="text-primary font-medium hover:underline transition-all duration-300"><EMAIL></a>
            </div>
          </div>
        </div>
      </section>

      {/* Client-side script for animations - only runs after hydration */}
      {isClient && (
        <script dangerouslySetInnerHTML={{
          __html: `
            // Usar requestAnimationFrame para asegurar que el script se ejecute después del renderizado
            window.requestAnimationFrame(function() {
              // Fade in sections on scroll
              const sections = document.querySelectorAll('.section-fade');
              const fadeInOnScroll = function() {
                sections.forEach(section => {
                  const sectionTop = section.getBoundingClientRect().top;
                  const windowHeight = window.innerHeight;
                  if (sectionTop < windowHeight * 0.85) {
                    section.classList.add('visible');
                  }
                });
              };
              // Initial check
              setTimeout(fadeInOnScroll, 100);
              // Check on scroll
              window.addEventListener('scroll', fadeInOnScroll);
            });
          `
        }} />
      )}
    </div>
  );
}
