# Plataforma de Certificados Educativos

Sistema integral de gestión educativa y emisión de certificados, desarrollado con Next.js y Supabase.

## Características

- **Autenticación y Autorización**: Sistema completo con diferentes roles (administrador, alumno) y políticas de seguridad RLS
- **Panel de Administración**: Gestión de alumnos, cursos, certificados, notas y asistencia
- **Panel de Alumno**: Visualización de certificados, notas y asistencia
- **Verificación de Certificados**: Sistema público de verificación mediante códigos QR
- **Almacenamiento**: Gestión de documentos y certificados en Supabase Storage

## Requisitos

- Node.js 18.x o superior
- NPM 8.x o superior
- Cuenta en Supabase

## Configuración

1. Clona el repositorio:

```bash
git clone https://github.com/iberi22/certifyQR.git
cd domus-otec
```

2. Instala las dependencias:

```bash
npm install
```

3. Copia el archivo de variables de entorno y configúralo:

```bash
cp .env.local.example .env.local
```

4. Edita `.env.local` con tus credenciales de Supabase.

5. Ejecuta el servidor de desarrollo:

```bash
npm run dev
```

## Generación de Datos de Prueba

El proyecto incluye scripts para generar datos de prueba en diferentes volúmenes:

### Preparación

Asegúrate de que las variables de entorno están configuradas correctamente en `.env.local`.

### Comandos disponibles

```bash
# Genera un pequeño conjunto de datos (desarrollo local)
npm run seed:small

# Genera un conjunto medio de datos (pruebas)
npm run seed:medium

# Genera un gran conjunto de datos (pruebas de rendimiento)
npm run seed:large

# Verifica la integridad de los datos generados
npm run seed:verify

# Limpia todos los datos de prueba (¡PRECAUCIÓN!)
npm run seed:cleanup
```

## Estructura del Proyecto

```
domus-otec/
├── src/
│   ├── app/                   # Rutas y páginas (Next.js App Router)
│   │   ├── panel-admin/       # Panel de administración
│   │   ├── panel-alumno/      # Panel de alumnos
│   │   └── verificar-certificado/ # Verificación pública de certificados
│   ├── components/            # Componentes reutilizables
│   ├── lib/                   # Utilidades y configuraciones
│   └── styles/                # Estilos globales
├── public/                    # Archivos públicos
├── seeds/                     # Scripts de generación de datos
│   ├── seed.ts                # Script principal de generación
│   ├── verify-seed.ts         # Verificación de integridad
│   └── cleanup.ts             # Limpieza de datos
└── docs/                      # Documentación
```

## Despliegue

La plataforma está optimizada para ser desplegada en Vercel con CI/CD automatizado:

```bash
# Construir localmente
yarn build

# Configurar despliegue automatizado
./setup-deployment.ps1
```

Para más información sobre el despliegue y el flujo de trabajo Git Flow, consulta:

- [Guía de Despliegue en Vercel](docs/VERCEL_DEPLOYMENT.md)
- [Guía de Git Flow](docs/GITFLOW.md)
- [Configuración Manual de Git Flow](docs/MANUAL_GITFLOW.md)

También puedes usar los scripts de ayuda para el flujo de trabajo:

```bash
# Iniciar una nueva característica
./git-flow-helper.ps1 -Action feature -Name nombre-caracteristica

# Finalizar una característica
./git-flow-helper.ps1 -Action finish-feature -Name nombre-caracteristica

# Iniciar una nueva versión
./git-flow-helper.ps1 -Action release -Name v1.0.0

# Finalizar una versión
./git-flow-helper.ps1 -Action finish-release -Name v1.0.0

# Iniciar un hotfix
./git-flow-helper.ps1 -Action hotfix -Name error-critico

# Finalizar un hotfix
./git-flow-helper.ps1 -Action finish-hotfix -Name error-critico
```

## Documentación Adicional

En la carpeta `docs/` puedes encontrar documentación detallada sobre:

- [Workflow y Estado del Proyecto](docs/workflow.md)
- [Estrategia de Datos de Prueba](docs/estrategia-datos-prueba.md)
- [Seguridad y RLS](docs/RLS_SECURITY.md)

## Licencia

Todos los derechos reservados © DOMUS OTEC 2023.
