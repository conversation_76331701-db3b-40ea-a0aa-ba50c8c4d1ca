"use client";

import { usePathname } from "next/navigation";
import Link from "next/link";
import { supabase } from "@/lib/supabase";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const router = useRouter();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [mounted, setMounted] = useState(false);

  // Este efecto asegura que el componente solo se renderice en el cliente
  // para evitar errores de hidratación
  useEffect(() => {
    setMounted(true);
  }, []);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    await supabase.auth.signOut();
    router.push("/login");
  };

  // Si no está montado, devolvemos un div vacío para evitar errores de hidratación
  if (!mounted) {
    return <div className="min-h-screen bg-gray-100"></div>;
  }

  return (
    <div className="min-h-screen bg-gray-100">
      <div className="bg-indigo-600 pb-32">
        <nav className="bg-indigo-600 border-b border-indigo-500 border-opacity-25 lg:border-none">
          <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
            <div className="relative h-16 flex items-center justify-between lg:border-b lg:border-indigo-400 lg:border-opacity-25">
              <div className="px-2 flex items-center lg:px-0">
                <div className="flex-shrink-0">
                  <Link href="/panel-admin" className="text-white font-bold text-xl">
                    DOMUS OTEC | Admin
                  </Link>
                </div>
                <div className="hidden lg:block lg:ml-10">
                  <div className="flex space-x-4">
                    <Link
                      href="/panel-admin"
                      className={`${
                        pathname === "/panel-admin"
                          ? "bg-indigo-700 text-white"
                          : "text-white hover:bg-indigo-500 hover:bg-opacity-75"
                      } rounded-md py-2 px-3 text-sm font-medium`}
                    >
                      Dashboard
                    </Link>
                    <Link
                      href="/panel-admin/alumnos"
                      className={`${
                        pathname === "/panel-admin/alumnos"
                          ? "bg-indigo-700 text-white"
                          : "text-white hover:bg-indigo-500 hover:bg-opacity-75"
                      } rounded-md py-2 px-3 text-sm font-medium`}
                    >
                      Alumnos
                    </Link>
                    <Link
                      href="/panel-admin/certificados"
                      className={`${
                        pathname === "/panel-admin/certificados"
                          ? "bg-indigo-700 text-white"
                          : "text-white hover:bg-indigo-500 hover:bg-opacity-75"
                      } rounded-md py-2 px-3 text-sm font-medium`}
                    >
                      Certificados
                    </Link>
                    {/* <Link
                      href="/panel-admin/notas-asistencia"
                      className={`${
                        pathname === "/panel-admin/notas-asistencia"
                          ? "bg-indigo-700 text-white"
                          : "text-white hover:bg-indigo-500 hover:bg-opacity-75"
                      } rounded-md py-2 px-3 text-sm font-medium`}
                    >
                      Notas y Asistencia
                    </Link> */}
                    <Link
                      href="/panel-admin/cursos"
                      className={`${
                        pathname.startsWith("/panel-admin/cursos")
                          ? "bg-indigo-700 text-white"
                          : "text-white hover:bg-indigo-500 hover:bg-opacity-75"
                      } rounded-md py-2 px-3 text-sm font-medium`}
                    >
                      Cursos
                    </Link>
                    <Link
                      href="/panel-admin/instructores"
                      className={`${
                        pathname.startsWith("/panel-admin/instructores")
                          ? "bg-indigo-700 text-white"
                          : "text-white hover:bg-indigo-500 hover:bg-opacity-75"
                      } rounded-md py-2 px-3 text-sm font-medium`}
                    >
                      Instructores
                    </Link>
                    {/* <Link
                      href="/panel-admin/plantillas"
                      className={`${
                        pathname.startsWith("/panel-admin/plantillas")
                          ? "bg-indigo-700 text-white"
                          : "text-white hover:bg-indigo-500 hover:bg-opacity-75"
                      } rounded-md py-2 px-3 text-sm font-medium`}
                    >
                      Plantillas
                    </Link> */}
                  </div>
                </div>
              </div>
              <div className="flex lg:hidden">
                {/* Mobile menu button */}
                <button
                  type="button"
                  className="bg-indigo-600 p-2 rounded-md inline-flex items-center justify-center text-indigo-200 hover:text-white hover:bg-indigo-500 hover:bg-opacity-75 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-indigo-600 focus:ring-white"
                  aria-expanded="false"
                  aria-controls="mobile-menu"
                  aria-label="Abrir menú principal"
                >
                  <svg
                    className="block h-6 w-6"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    aria-hidden="true"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth="2"
                      d="M4 6h16M4 12h16M4 18h16"
                    />
                  </svg>
                </button>
              </div>
              <div className="hidden lg:block lg:ml-4">
                <div className="flex items-center">
                  <button
                    onClick={handleLogout}
                    disabled={isLoggingOut}
                    className="flex-shrink-0 bg-indigo-600 p-1 text-indigo-200 rounded-full hover:text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-indigo-600 focus:ring-white"
                  >
                    <span className="sr-only">Cerrar sesión</span>
                    <svg
                      className="h-6 w-6"
                      xmlns="http://www.w3.org/2000/svg"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                      />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </nav>
        <header className="py-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <h1 className="text-3xl font-bold text-white">
              {pathname === "/panel-admin" && "Panel de Administración"}
              {pathname === "/panel-admin/alumnos" && "Gestión de Alumnos"}
              {pathname === "/panel-admin/certificados" && "Gestión de Certificados"}
              {pathname === "/panel-admin/notas-asistencia" && "Notas y Asistencia"}
              {pathname.startsWith("/panel-admin/cursos") && "Gestión de Cursos"}
              {pathname.startsWith("/panel-admin/instructores") && "Gestión de Instructores"}
              {pathname.startsWith("/panel-admin/plantillas") && "Plantillas de Certificados"}
            </h1>
          </div>
        </header>
      </div>

      <main className="-mt-32">
        <div className="max-w-7xl mx-auto pb-12 px-4 sm:px-6 lg:px-8">
          <div className="bg-white rounded-lg shadow px-5 py-6 sm:px-6">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}