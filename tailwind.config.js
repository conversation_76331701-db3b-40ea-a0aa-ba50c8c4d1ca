/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          light: '#1DA1F2',
          DEFAULT: '#1DA1F2',
          dark: '#0C8BD9',
        },
        secondary: {
          light: '#9F6EF3',
          DEFAULT: '#7B3FE4',
          dark: '#6232C5',
        },
        gradient: {
          start: '#1DA1F2',
          end: '#7B3FE4',
        },
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, var(--tw-gradient-stops))',
      },
      gradientColorStops: {
        'gradient-start': '#1DA1F2',
        'gradient-end': '#7B3FE4',
      },
    },
  },
  plugins: [],
  // Deshabilitar el compilador Oxide para evitar problemas de compatibilidad
  oxide: false,
}
