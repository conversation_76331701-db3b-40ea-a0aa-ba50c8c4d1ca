# Reglas de Contexto para Proyecto NVP Plataforma Certificados (Next.js + Supabase)

**Propósito:** Este documento define el contexto esencial y las reglas fundamentales que deben seguirse al desarrollar el NVP de la Plataforma Educativa y de Certificados. **Siempre** utiliza este contexto como base para cualquier tarea de codificación. Consulta el prompt original detallado si necesitas especificaciones más profundas sobre una funcionalidad concreta.

---

**1. Resumen del Proyecto:**

* **Objetivo:** Crear un NVP funcional **rápidamente** para una plataforma web de gestión educativa/certificados.
* **Backend:** **Supabase** (Auth, DB PostgreSQL, Storage, Edge Functions).
* **Frontend:** **Next.js** (App Router, TypeScript).
* **Foco:** Funcionalidad esencial para pruebas y validación, velocidad de desarrollo, diseño limpio/moderno.
* **Público:** Administradores, Alumnos/Usuarios, Público General (verificación QR).

---

**2. Stack Tecnológico Principal (Obligatorio - Últimas Versiones Estables):**

* **Framework:** Next.js (App Router, TypeScript)
* **Backend/DB:** Supabase Platform
* **Interacción DB/Backend:** **@supabase/supabase-js** (Client Library) - **Priorizar sobre Prisma para NVP.**
* **UI/Estilos:** Tailwind CSS + **Shadcn/UI** (Usar componentes pre-construidos).
* **Generación QR:** `qrcode` (Node.js - usar en API Routes / Edge Functions).
* **Despliegue:** Vercel (Frontend), Supabase (Backend).

---

**3. Instrucción Crítica Obligatoria: Base de Datos Supabase Existente**

* **¡PRIORIDAD MÁXIMA ANTES DE CODIFICAR MODELOS/APIs!**
* **Revisar:** Inspeccionar **detalladamente** la estructura de la base de datos PostgreSQL **existente** en el proyecto Supabase proporcionado.
* **Analizar:** Identificar tablas/columnas/relaciones relevantes (`users`, `profiles`, `students`, `certificates`, `courses`, etc.).
* **Adaptar o Modificar:**
  * Si el esquema existente **sirve**: Adaptar el código para **usarlo**.
  * Si el esquema existente **NO sirve**: Documentar y **realizar las modificaciones necesarias** (SQL en editor Supabase o Migraciones Supabase).
* **Implementar:** Basar **todo** el código de backend/DB en el esquema **revisado y finalizado**.
* **RLS:** Configurar **Row Level Security** en Supabase como parte fundamental de la seguridad de datos.

---

**4. Módulos Principales del NVP (Scope):**

1. Landing Page Estática (`/`)
2. Autenticación y Roles (Supabase Auth, tabla `profiles` con roles, Middleware)
3. Gestión de Certificados (Admin: CRUD simplificado, Generación/Almacenamiento QR; Alumno: Ver/Descargar) - **Considerar Edge Function para QR.**
4. Gestión de Alumnos (Admin: CRUD completo).
5. Notas/Asistencia (Admin: Registro simple; Alumno: Ver).
6. Evaluaciones (Admin: Registro simple; Alumno: Ver).
7. Verificación QR Pública (`/verificar-certificado`).

---

**5. Flujo de Trabajo General (Hitos):**

1. **Setup & Auth:** Next.js init, Conexión Supabase, Auth UI/Lógica, Layouts, Middleware.
2. **DB & API:** **Revisión/Adaptación DB (CRÍTICO)**, API Routes (Next.js) / Edge Functions (Supabase), RLS.
3. **UI & Funcionalidades:** Shadcn UI, Conexión UI-Backend (`supabase-js`), Validaciones.
4. **Refinamiento MVP:** Testing básico, Optimización, Preparación Deploy.

---

**6. Principios de Codificación:**

* **Lenguaje:** TypeScript claro y bien tipado.
* **Estilo:** Seguir convenciones de Next.js, React, Tailwind. Comentar lógica compleja.
* **Errores:** Manejo básico de errores (frontend/backend).
* **Seguridad:** **Priorizar RLS en Supabase.** Validar entradas en backend. No exponer secretos.
* **MVP Estricto:** Enfocarse **solo** en las funcionalidades definidas en el prompt NVP. Evitar complejidad o features adicionales. **La velocidad es clave.**

---

**Recordatorio:** Este archivo es tu guía principal de contexto. Si alguna instrucción aquí entra en conflicto con una solicitud posterior, prioriza este documento o pide clarificación. Para detalles específicos de implementación de cada módulo, consulta el prompt original completo. nunca mover los archivos .env y .env.local a git, ni lanzar modificaciones a estos